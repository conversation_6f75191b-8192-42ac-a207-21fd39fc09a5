// Types for Client Processes (Procesos de Clientes)

export type UnidadRepeticion = 
  | 'Diario'
  | 'Semanal'
  | 'Quincenal'
  | 'Mensual'
  | 'Bimestral'
  | 'Trimestral'
  | 'Anual'
  | 'Puntual'
  | 'Bajo Demanda';

export type ValorNegocioCliente = 
  | 'Bajo'
  | 'Medio'
  | 'Alto'
  | 'Crítico';

export type ComplejidadAutomatizacion =
  | 'Baja'
  | 'Media'
  | 'Medio'
  | 'Baja-Media'
  | 'Alta'
  | 'Muy Alta';

export type PrioridadAutomatizacion =
  | 'Baja'
  | 'Media'
  | 'Medio'
  | 'Alta'
  | 'Urgente';

export type EstadoAnalisis =
  | 'Pendiente'
  | 'En Análisis'
  | 'Analizado'
  | 'Implementado'
  | 'Descartado'
  | 'Análisis Experto Realizado'
  | 'identificado'; // Legacy value found in database

export type ProcesoClienteGroupBy =
  | 'responsable_principal'
  | 'departamento'
  | 'prioridad_automatizacion';

// Type for herramientas_utilizadas_cliente field which stores JSON data
export type HerramientasUtilizadasCliente = Record<string, unknown>;

// Base interfaces
export interface PersonaResponsable {
  id: string;
  nombre: string;
  apellidos?: string;
  cargo?: string;
  departamento_nombre?: string;
}

export interface DepartamentoInfo {
  id: string;
  nombre: string;
  descripcion?: string;
}

export interface ProcesoClienteBase {
  id: string;
  empresa_cliente_id: string;
  nombre: string;
  descripcion?: string;
  estado_analisis?: EstadoAnalisis;
  es_repetitivo?: boolean;
  es_cuello_botella?: boolean;
  es_manual?: boolean;
  valor_negocio_cliente?: string;
  complejidad_automatizacion_aceleralia?: string;
  prioridad_automatizacion_aceleralia?: string;
  duracion_minutos_por_ejecucion?: number;
  frecuencia_periodo?: string;
  frecuencia_ocurrencias?: number;
  herramientas_utilizadas_cliente?: HerramientasUtilizadasCliente;
  reunion_origen_id?: string;
  info_adicional?: string;
  created_at: string;
  updated_at: string;
}

export interface ProcesoClienteListItem extends ProcesoClienteBase {
  // Calculated fields
  tiempo_estimado_horas_mes: number;
  numero_tareas: number;
  
  // Related data
  responsable_principal?: PersonaResponsable;
  departamento?: DepartamentoInfo;
  otros_responsables: PersonaResponsable[];
}

export interface ProcesoClienteDetalle extends ProcesoClienteBase {
  // Calculated fields
  tiempo_estimado_horas_mes: number;
  numero_tareas: number;
  
  // Related data
  responsable_principal?: PersonaResponsable;
  departamento?: DepartamentoInfo;
  otros_responsables: PersonaResponsable[];
  tareas: TareaClienteDetalle[];
}

// Task interfaces
export interface TareaClienteBase {
  id: string;
  proceso_cliente_id: string;
  nombre_tarea_cliente: string;
  descripcion_tarea_cliente?: string;
  duracion_minutos_por_ejecucion?: number;
  frecuencia_periodo?: string;
  frecuencia_ocurrencias?: number;
  es_manual_cliente?: boolean;
  herramientas_utilizadas_cliente?: HerramientasUtilizadasCliente;
  puntos_dolor_cliente?: string;
  oportunidades_mejora_cliente?: string;
  info_adicional?: string;
  created_at: string;
  updated_at: string;
}

export interface TareaClienteListItem extends TareaClienteBase {
  tiempo_estimado_horas_mes: number;
  responsables: PersonaResponsable[];
}

export interface TareaClienteDetalle extends TareaClienteBase {
  tiempo_estimado_horas_mes: number;
  responsables: PersonaResponsable[];
}

// Filter interfaces
export interface ProcesoClienteFilters {
  responsable_ids?: string[];
  departamento_id?: string;
  es_cuello_botella?: boolean;
  valor_negocio_cliente?: string;
  es_manual?: boolean;
  es_repetitivo?: boolean;
  complejidad_automatizacion_aceleralia?: string;
  prioridad_automatizacion_aceleralia?: string;
  search?: string;
}

export interface TareaClienteFilters {
  responsable_ids?: string[];
  es_manual_cliente?: boolean;
  search?: string;
}

// Response interfaces
export interface ProcesoClienteListResponse {
  procesos: ProcesoClienteListItem[];
  total: number;
  tiempo_total_horas_mes: number;
  total_tareas: number;
}

export interface TareaClienteListResponse {
  tareas: TareaClienteListItem[];
  total: number;
  tiempo_total_horas_mes: number;
}

export interface ProcesoClienteContadores {
  total_procesos: number;
  total_tareas: number;
  tiempo_total_horas_mes: number;
}

// Update interfaces
export interface ProcesoClienteUpdate {
  nombre?: string;
  descripcion?: string;
  departamento_cliente_id?: string;
  estado_analisis?: EstadoAnalisis;
  es_repetitivo?: boolean;
  es_cuello_botella?: boolean;
  es_manual?: boolean;
  valor_negocio_cliente?: string;
  complejidad_automatizacion_aceleralia?: string;
  prioridad_automatizacion_aceleralia?: string;
  duracion_minutos_por_ejecucion?: number;
  frecuencia_periodo?: string;
  frecuencia_ocurrencias?: number;
  herramientas_utilizadas_cliente?: HerramientasUtilizadasCliente;
  info_adicional?: string;
}

export interface TareaClienteUpdate {
  nombre_tarea_cliente?: string;
  descripcion_tarea_cliente?: string;
  duracion_minutos_por_ejecucion?: number;
  frecuencia_periodo?: string;
  frecuencia_ocurrencias?: number;
  es_manual_cliente?: boolean;
  herramientas_utilizadas_cliente?: HerramientasUtilizadasCliente;
  puntos_dolor_cliente?: string;
  oportunidades_mejora_cliente?: string;
  info_adicional?: string;
}

// Constants
export const UNIDADES_REPETICION: UnidadRepeticion[] = [
  'Diario',
  'Semanal',
  'Quincenal',
  'Mensual',
  'Bimestral',
  'Trimestral',
  'Anual',
  'Puntual',
  'Bajo Demanda'
];

export const VALORES_NEGOCIO_CLIENTE: ValorNegocioCliente[] = [
  'Bajo',
  'Medio',
  'Alto',
  'Crítico'
];

export const COMPLEJIDADES_AUTOMATIZACION: ComplejidadAutomatizacion[] = [
  'Baja',
  'Media',
  'Medio',
  'Baja-Media',
  'Alta',
  'Muy Alta'
];

export const PRIORIDADES_AUTOMATIZACION: PrioridadAutomatizacion[] = [
  'Baja',
  'Media',
  'Medio',
  'Alta',
  'Urgente'
];

export const ESTADOS_ANALISIS: EstadoAnalisis[] = [
  'Pendiente',
  'En Análisis',
  'Analizado',
  'Implementado',
  'Descartado',
  'Análisis Experto Realizado'
];

// Color mappings for UI
export const VALOR_NEGOCIO_COLORS: Record<ValorNegocioCliente, string> = {
  'Bajo': 'bg-gray-100 text-gray-800',
  'Medio': 'bg-yellow-100 text-yellow-800',
  'Alto': 'bg-orange-100 text-orange-800',
  'Crítico': 'bg-red-100 text-red-800'
};

export const PRIORIDAD_AUTOMATIZACION_COLORS: Record<PrioridadAutomatizacion, string> = {
  'Baja': 'bg-gray-100 text-gray-800',
  'Media': 'bg-blue-100 text-blue-800',
  'Medio': 'bg-blue-100 text-blue-800',
  'Alta': 'bg-orange-100 text-orange-800',
  'Urgente': 'bg-red-100 text-red-800'
};

export const COMPLEJIDAD_AUTOMATIZACION_COLORS: Record<ComplejidadAutomatizacion, string> = {
  'Baja': 'bg-green-100 text-green-800',
  'Media': 'bg-yellow-100 text-yellow-800',
  'Medio': 'bg-yellow-100 text-yellow-800',
  'Baja-Media': 'bg-yellow-100 text-yellow-800',
  'Alta': 'bg-orange-100 text-orange-800',
  'Muy Alta': 'bg-red-100 text-red-800'
};

export const ESTADO_ANALISIS_COLORS: Record<EstadoAnalisis, string> = {
  'Pendiente': 'bg-gray-100 text-gray-800',
  'En Análisis': 'bg-blue-100 text-blue-800',
  'Analizado': 'bg-green-100 text-green-800',
  'Implementado': 'bg-emerald-100 text-emerald-800',
  'Descartado': 'bg-red-100 text-red-800',
  'Análisis Experto Realizado': 'bg-purple-100 text-purple-800',
  'identificado': 'bg-yellow-100 text-yellow-800' // Legacy value found in database
};
