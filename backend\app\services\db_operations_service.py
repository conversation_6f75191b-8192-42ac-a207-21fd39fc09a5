"""
Database Operations Service

This service handles INSERT, UPDATE, and DELETE operations using the read-write database pool.
It provides secure execution of database mutations with structured JSON input.

Features:
- Read-write database pool usage
- Structured JSON input validation
- SQL injection prevention through parameterized queries
- Identifier validation (table names, column names)
- Mandatory WHERE clauses for UPDATE/DELETE
- RETURNING clause support
- Comprehensive error handling and logging
"""

import logging
import json
from typing import List, Dict, Any, Optional, Tuple
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, status
import asyncpg

from ..core.database import get_db_pool_readwrite
from ..models.tool import (
    DbOpRequest, DbOpResponse, InsertRequest, UpdateRequest, DeleteRequest,
    validate_identifier
)

logger = logging.getLogger(__name__)

class DbOperationsService:
    """Service for handling database mutation operations (INSERT, UPDATE, DELETE)."""

    def serialize_json_values(self, value: Any) -> Any:
        """
        Serializes objects and arrays to JSON strings for database storage.

        This function handles the automatic conversion of Python objects and lists
        to JSON strings, which is required for PostgreSQL jsonb columns.

        Args:
            value: The value to potentially serialize

        Returns:
            JSON string if value is an object/array, otherwise the original value
        """
        # Check if the value is an object (dict) or array (list) and not None
        if isinstance(value, (dict, list)) and value is not None:
            try:
                return json.dumps(value, ensure_ascii=False)
            except (TypeError, ValueError) as e:
                logger.warning(f"Failed to serialize value to JSON: {value}. Error: {e}")
                # Return the original value if serialization fails
                return value
        else:
            # Return the original value for non-object/array types
            return value

    async def validate_where_clause(self, where_conditions: Dict[str, Any]) -> None:
        """
        Validates that WHERE conditions are not empty for UPDATE/DELETE operations.
        
        Args:
            where_conditions: Dictionary of WHERE conditions
            
        Raises:
            HTTPException 400: If WHERE conditions are empty
        """
        if not where_conditions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="WHERE conditions are required for UPDATE and DELETE operations"
            )
    
    async def build_insert_query(self, request: InsertRequest) -> Tuple[str, List[Any]]:
        """
        Builds a parameterized INSERT query from the request.
        
        Args:
            request: INSERT request object
            
        Returns:
            Tuple of (SQL query, parameter values)
        """
        # Validate table name
        table = validate_identifier(request.table)
        
        if not request.records:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one record is required for INSERT operation"
            )
        
        # Get column names from the first record (all records should have same structure)
        columns = list(request.records[0].keys())
        
        # Validate column names
        validated_columns = [validate_identifier(col) for col in columns]
        
        # Build the base INSERT query
        columns_str = ", ".join(validated_columns)

        # For multiple records, we need multiple value groups
        all_placeholders = []
        all_values = []
        param_counter = 1
        
        for record in request.records:
            # Ensure all records have the same columns
            if set(record.keys()) != set(columns):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="All records must have the same column structure"
                )
            
            # Build placeholders for this record
            record_placeholders = []
            for col in columns:
                record_placeholders.append(f"${param_counter}")
                # Serialize JSON objects/arrays before adding to values
                serialized_value = self.serialize_json_values(record[col])
                all_values.append(serialized_value)
                param_counter += 1
            
            all_placeholders.append(f"({', '.join(record_placeholders)})")
        
        values_str = ", ".join(all_placeholders)
        
        # Build the complete query
        query = f"INSERT INTO {table} ({columns_str}) VALUES {values_str}"
        
        # Add RETURNING clause if specified
        if request.returning:
            validated_returning = [validate_identifier(col) for col in request.returning]
            returning_str = ", ".join(validated_returning)
            query += f" RETURNING {returning_str}"
        
        return query, all_values
    
    async def build_update_query(self, request: UpdateRequest) -> Tuple[str, List[Any]]:
        """
        Builds a parameterized UPDATE query from the request.
        
        Args:
            request: UPDATE request object
            
        Returns:
            Tuple of (SQL query, parameter values)
        """
        # Validate table name
        table = validate_identifier(request.table)
        
        # Validate WHERE clause
        await self.validate_where_clause(request.where)
        
        if not request.set:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="SET clause cannot be empty for UPDATE operation"
            )
        
        # Build SET clause
        set_clauses = []
        values = []
        param_counter = 1
        
        for column, value in request.set.items():
            validated_column = validate_identifier(column)
            set_clauses.append(f"{validated_column} = ${param_counter}")
            # Serialize JSON objects/arrays before adding to values
            serialized_value = self.serialize_json_values(value)
            values.append(serialized_value)
            param_counter += 1
        
        set_str = ", ".join(set_clauses)
        
        # Build WHERE clause
        where_clauses = []
        for column, value in request.where.items():
            validated_column = validate_identifier(column)
            where_clauses.append(f"{validated_column} = ${param_counter}")
            # Serialize JSON objects/arrays before adding to values
            serialized_value = self.serialize_json_values(value)
            values.append(serialized_value)
            param_counter += 1
        
        where_str = " AND ".join(where_clauses)
        
        # Build the complete query
        query = f"UPDATE {table} SET {set_str} WHERE {where_str}"
        
        # Add RETURNING clause if specified
        if request.returning:
            validated_returning = [validate_identifier(col) for col in request.returning]
            returning_str = ", ".join(validated_returning)
            query += f" RETURNING {returning_str}"
        
        return query, values
    
    async def build_delete_query(self, request: DeleteRequest) -> Tuple[str, List[Any]]:
        """
        Builds a parameterized DELETE query from the request.
        
        Args:
            request: DELETE request object
            
        Returns:
            Tuple of (SQL query, parameter values)
        """
        # Validate table name
        table = validate_identifier(request.table)
        
        # Validate WHERE clause
        await self.validate_where_clause(request.where)
        
        # Build WHERE clause
        where_clauses = []
        values = []
        param_counter = 1
        
        for column, value in request.where.items():
            validated_column = validate_identifier(column)
            where_clauses.append(f"{validated_column} = ${param_counter}")
            # Serialize JSON objects/arrays before adding to values
            serialized_value = self.serialize_json_values(value)
            values.append(serialized_value)
            param_counter += 1
        
        where_str = " AND ".join(where_clauses)
        
        # Build the complete query
        query = f"DELETE FROM {table} WHERE {where_str}"
        
        # Add RETURNING clause if specified
        if request.returning:
            validated_returning = [validate_identifier(col) for col in request.returning]
            returning_str = ", ".join(validated_returning)
            query += f" RETURNING {returning_str}"
        
        return query, values

    async def execute_db_operation(self, request: DbOpRequest) -> DbOpResponse:
        """
        Executes a database operation (INSERT, UPDATE, or DELETE).

        Args:
            request: Database operation request

        Returns:
            DbOpResponse with operation results

        Raises:
            HTTPException 400: If validation fails
            HTTPException 500: If database execution fails
        """
        logger.info(f"Executing {request.operation.upper()} operation on table '{request.table}'")

        try:
            # Build the appropriate query based on operation type
            if isinstance(request, InsertRequest):
                query, values = await self.build_insert_query(request)
            elif isinstance(request, UpdateRequest):
                query, values = await self.build_update_query(request)
            elif isinstance(request, DeleteRequest):
                query, values = await self.build_delete_query(request)
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Unsupported operation: {request.operation}"
                )

            logger.debug(f"Generated query: {query}")
            logger.debug(f"Parameter count: {len(values)}")

            # Get the read-write database pool and execute with proper transaction management
            pool = await get_db_pool_readwrite()

            # Use pool.acquire() as context manager and explicit transaction
            async with pool.acquire() as connection:
                async with connection.transaction():
                    if request.returning:
                        # Query with RETURNING clause - fetch results
                        rows = await connection.fetch(query, *values)
                        results = [dict(row) for row in rows]
                        rows_affected = len(results)

                        logger.info(f"{request.operation.upper()} operation completed. Rows affected: {rows_affected}")
                        return DbOpResponse(
                            rows_affected=rows_affected,
                            result=results
                        )
                    else:
                        # Query without RETURNING clause - get actual row count from PostgreSQL
                        status_result = await connection.execute(query, *values)

                        # Extract the actual number of affected rows from PostgreSQL status
                        # Status format: "INSERT 0 6", "UPDATE 3", "DELETE 2", etc.
                        import re
                        match = re.search(r'\b(\d+)$', status_result or '')
                        rows_affected = int(match.group(1)) if match else 0

                        logger.debug(f"PostgreSQL status: {status_result}, extracted rows_affected: {rows_affected}")

                        # Verify that the operation actually affected rows when it should have
                        if request.operation == "insert" and hasattr(request, 'records') and len(request.records) > 0:
                            if rows_affected == 0:
                                raise HTTPException(
                                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                                    detail=f"INSERT operation failed: Expected to insert {len(request.records)} records but 0 rows were affected"
                                )
                        elif request.operation in ["update", "delete"] and rows_affected == 0:
                            # For UPDATE/DELETE, 0 rows affected might be valid (no matching records)
                            # but we should log it for debugging
                            logger.warning(f"{request.operation.upper()} operation affected 0 rows - this might indicate no matching records")

                        logger.info(f"{request.operation.upper()} operation completed. Rows affected: {rows_affected}")
                        return DbOpResponse(rows_affected=rows_affected)

        except HTTPException:
            # Re-raise HTTP exceptions (validation errors)
            raise
        except asyncpg.PostgresError as db_error:
            # Handle database-specific errors
            logger.error(f"Database error executing {request.operation.upper()}: {db_error}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(db_error)}"
            )
        except Exception as e:
            # Handle unexpected errors
            logger.error(f"Unexpected error executing {request.operation.upper()}: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An unexpected error occurred: {str(e)}"
            )

# Create service instance
db_operations_service = DbOperationsService()
